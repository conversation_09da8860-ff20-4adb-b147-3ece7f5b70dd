# Landing Page Configuration Guide

This project includes a fully functional, customizable landing page built with Astro. All content can be easily customized by modifying the configuration file.

## Quick Start

1. **Customize your content**: Edit `src/config/landingPage.js`
2. **Build the project**: Run `npm run build`
3. **Preview locally**: Run `npm run dev` and visit `http://localhost:4321`

## Configuration File Structure

The landing page is configured through `src/config/landingPage.js`. Here's what each section controls:

### Site Metadata
```javascript
site: {
  title: "Your Company Name",           // Browser tab title
  description: "Your site description", // Meta description for SEO
  keywords: "your, keywords, here"      // Meta keywords for SEO
}
```

### Hero Section
```javascript
hero: {
  title: "Main headline",               // Large title text
  subtitle: "Supporting headline",      // Smaller subtitle
  description: "Descriptive text",      // Paragraph below titles
  primaryButton: {
    text: "Button text",
    href: "#section-id"                 // Link destination
  },
  secondaryButton: {
    text: "Button text",
    href: "#section-id"
  }
}
```

### Features Section
```javascript
features: {
  title: "Section title",
  subtitle: "Section subtitle",
  items: [
    {
      title: "Feature name",
      description: "Feature description",
      icon: "🚀"                        // Emoji or icon
    }
    // Add more features as needed
  ]
}
```

### About Section
```javascript
about: {
  title: "About title",
  description: "About description",
  stats: [
    {
      number: "100+",                   // Statistic number
      label: "Statistic label"          // What the number represents
    }
    // Add more stats as needed
  ]
}
```

### Services Section
```javascript
services: {
  title: "Services title",
  subtitle: "Services subtitle",
  items: [
    {
      title: "Service name",
      description: "Service description",
      features: [                       // List of service features
        "Feature 1",
        "Feature 2",
        "Feature 3"
      ]
    }
    // Add more services as needed
  ]
}
```

### Call to Action Section
```javascript
cta: {
  title: "CTA title",
  description: "CTA description",
  button: {
    text: "Button text",
    href: "#contact"
  }
}
```

### Contact Section
```javascript
contact: {
  title: "Contact title",
  description: "Contact description",
  email: "<EMAIL>",
  phone: "+****************",
  address: "Your business address"
}
```

### Footer
```javascript
footer: {
  companyName: "Your Company",
  copyright: "All rights reserved.",
  links: [
    {
      title: "Link text",
      href: "/page-url"
    }
  ],
  social: [
    {
      name: "Platform name",
      href: "https://social-url.com",
      icon: "🐦"                        // Emoji or icon
    }
  ]
}
```

## Customization Tips

1. **Colors**: The landing page uses a purple gradient theme. To change colors, edit the CSS variables in `src/components/LandingPage.astro`

2. **Fonts**: The page uses Inter font family. You can change this in the CSS

3. **Sections**: You can add, remove, or reorder sections by modifying the `LandingPage.astro` component

4. **Icons**: Use emojis for simple icons, or replace with SVG icons for more professional look

5. **Links**: Use `#section-id` for internal navigation or full URLs for external links

## File Structure

```
src/
├── config/
│   └── landingPage.js          # Main configuration file
├── components/
│   └── LandingPage.astro       # Landing page component
├── layouts/
│   └── Layout.astro            # HTML layout with meta tags
└── pages/
    └── index.astro             # Main page that renders the landing page
```

## Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally

## Ready to Deploy

After customizing your content in `src/config/landingPage.js`, your landing page is ready to deploy to any static hosting service like Netlify, Vercel, or GitHub Pages.

The built files will be in the `dist/` directory after running `npm run build`.
