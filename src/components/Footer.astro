---
import { appLandingConfig } from '../config/landingPage.js';
import { Twitter, Instagram, Linkedin, Youtube } from 'lucide-astro';

const { footer } = appLandingConfig;

// Social icon mapping
const socialIconMap = {
  'Twitter': Twitter,
  'Instagram': Instagram,
  'Linkedin': Linkedin,
  'Youtube': Youtube
};
---

<footer class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-info">
        <h3>{footer.appName}</h3>
        <p class="footer-tagline">{footer.tagline}</p>
        <p class="footer-copyright">
          &copy; {footer.legal.year} {footer.legal.company}. {footer.copyright}
        </p>
        <p class="footer-address">{footer.legal.address}</p>
      </div>
      <div class="footer-links">
        <h4>Links</h4>
        {footer.links.map((link) => (
          <a href={link.href}>{link.title}</a>
        ))}
      </div>
      <div class="footer-social">
        <h4>Follow Us</h4>
        <div class="social-links">
          {footer.social.map((social) => {
            const SocialIcon = socialIconMap[social.icon];
            return (
              <a href={social.href} title={social.name} target="_blank" rel="noopener noreferrer">
                {SocialIcon && <SocialIcon class="social-icon" />}
              </a>
            );
          })}
        </div>
      </div>
    </div>
  </div>
</footer>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* Footer */
  .footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 30px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
  }

  .footer-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .footer-tagline {
    color: #ccc;
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .footer-copyright {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .footer-address {
    color: #999;
    font-size: 0.8rem;
  }

  .footer-links h4,
  .footer-social h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .footer-links a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
  }

  .footer-links a:hover {
    color: white;
  }

  .social-links {
    display: flex;
    gap: 1rem;
  }

  .social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .social-links a:hover {
    background: rgba(102, 126, 234, 0.2);
    color: white;
    transform: translateY(-2px);
  }

  .social-icon {
    width: 20px;
    height: 20px;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .container {
      padding: 0 20px;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .footer-content {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 3rem;
    }

    .footer {
      padding: 40px 0 20px;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 12px;
    }

    .footer-content {
      gap: 2rem;
    }

    .footer {
      padding: 30px 0 15px;
    }
  }
</style>
