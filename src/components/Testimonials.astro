---
import { appLandingConfig } from '../config/landingPage.js';

const { testimonials } = appLandingConfig;
---

<section id="testimonials" class="testimonials">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{testimonials.title}</h2>
      <p class="section-subtitle">{testimonials.subtitle}</p>
    </div>
    <div class="testimonials-carousel">
      <div class="testimonials-slider">
        {testimonials.items.map((testimonial) => (
          <div class="testimonial-card" data-animate="fade-up">
            <div class="testimonial-content">
              <div class="stars">
                {Array.from({ length: 5 }, (_, i) => (
                  <span class={`star ${i < testimonial.rating ? 'filled' : ''}`}>★</span>
                ))}
              </div>
              <p class="testimonial-text">"{testimonial.text}"</p>
              <div class="testimonial-author">
                <img
                  src={testimonial.avatar}
                  alt={`${testimonial.name} avatar`}
                  class="author-avatar"
                  loading="lazy"
                />
                <div class="author-info">
                  <h4 class="author-name">{testimonial.name}</h4>
                  <p class="author-role">{testimonial.role} at {testimonial.company}</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>

<style>
  .testimonials {
    padding: 80px 0;
    background: #f8fafc;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-align: center;
    color: #111827;
    letter-spacing: -0.025em;
  }

  .section-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }

  .section-header {
    margin-bottom: 3rem;
  }

  .testimonials-carousel {
    overflow-x: auto;
    padding: 0.5rem 0;
    margin: 0 -24px;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .testimonials-carousel::-webkit-scrollbar {
    display: none;
  }

  .testimonials-slider {
    display: flex;
    gap: 2rem;
    padding: 0 24px;
    min-width: max-content;
  }

  .testimonial-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    min-width: 320px;
    max-width: 320px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
  }

  .testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);
    border-color: rgba(102, 126, 234, 0.2);
  }

  .testimonial-card:hover::before {
    transform: scaleX(1);
  }

  .testimonial-content {
    flex-grow: 1;
    margin-bottom: 1.5rem;
  }

  .stars {
    display: flex;
    gap: 0.2rem;
    margin-bottom: 1rem;
    justify-content: flex-start;
  }

  .star {
    font-size: 1rem;
    color: #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .star.filled {
    color: #fbbf24;
  }

  .testimonial-text {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #374151;
    font-style: italic;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
  }

  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 1;
  }

  .author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(102, 126, 234, 0.2);
    flex-shrink: 0;
  }

  .author-info {
    flex-grow: 1;
  }

  .author-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.2rem;
  }

  .author-role {
    font-size: 0.8rem;
    color: #6b7280;
  }

  /* Animation for scroll reveal */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .container {
      padding: 0 20px;
    }

    .testimonials-carousel {
      margin: 0 -20px;
    }

    .testimonials-slider {
      padding: 0 20px;
      gap: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .testimonials {
      padding: 60px 0;
    }

    .section-title {
      font-size: 2rem;
    }

    .section-subtitle {
      font-size: 1rem;
      margin-bottom: 2rem;
    }

    .section-header {
      margin-bottom: 2rem;
    }

    .testimonials-carousel {
      margin: 0 -16px;
    }

    .testimonials-slider {
      padding: 0 16px;
      gap: 1.5rem;
    }

    .testimonial-card {
      min-width: 280px;
      max-width: 280px;
      padding: 1.25rem;
    }

    .testimonial-text {
      font-size: 0.9rem;
    }

    .author-avatar {
      width: 36px;
      height: 36px;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 12px;
    }

    .section-title {
      font-size: 1.75rem;
    }

    .testimonials-carousel {
      margin: 0 -12px;
    }

    .testimonials-slider {
      padding: 0 12px;
      gap: 1rem;
    }

    .testimonial-card {
      min-width: 260px;
      max-width: 260px;
      padding: 1rem;
    }

    .testimonial-text {
      font-size: 0.85rem;
      margin-bottom: 1rem;
    }

    .author-avatar {
      width: 32px;
      height: 32px;
    }

    .author-name {
      font-size: 0.85rem;
    }

    .author-role {
      font-size: 0.75rem;
    }

    .stars {
      margin-bottom: 0.75rem;
    }

    .star {
      font-size: 0.9rem;
    }
  }

  /* Hover effects for stars */
  .testimonial-card:hover .star.filled {
    color: #f59e0b;
    transform: scale(1.1);
  }

  /* Loading animation for avatars */
  .author-avatar {
    background: #f3f4f6;
    position: relative;
    overflow: hidden;
  }

  .author-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
</style>

<script>
  // Scroll animations for testimonials
  document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.testimonial-card[data-animate]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          // Add staggered animation delay
          setTimeout(() => {
            entry.target.classList.add('animate');
          }, index * 150);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  });
</script>
