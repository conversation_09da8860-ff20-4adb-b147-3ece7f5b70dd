---
import { appLandingConfig } from '../config/landingPage.js';

const { faq } = appLandingConfig;
---

<section id="faq" class="faq">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{faq.title}</h2>
      <p class="section-subtitle">{faq.subtitle}</p>
    </div>
    <div class="faq-container">
      {faq.items.map((item, index) => (
        <div class="faq-item" data-animate="fade-up">
          <button class="faq-question" data-faq-toggle={index}>
            <span class="question-text">{item.question}</span>
            <span class="faq-icon">+</span>
          </button>
          <div class="faq-answer" data-faq-content={index}>
            <div class="answer-content">
              <p>{item.answer}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>

<style>
  .faq {
    padding: 80px 0;
    background: white;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
    color: #1a1a1a;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-bottom: 3rem;
  }

  .faq-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .faq-item {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .faq-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  }

  .faq-question {
    width: 100%;
    padding: 1.5rem;
    background: white;
    border: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    transition: all 0.3s ease;
  }

  .faq-question:hover {
    background: #f9fafb;
  }

  .faq-question.active {
    background: #f0f4ff;
    color: #667eea;
  }

  .question-text {
    flex-grow: 1;
    margin-right: 1rem;
  }

  .faq-icon {
    font-size: 1.5rem;
    font-weight: 300;
    color: #667eea;
    transition: transform 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
  }

  .faq-question.active .faq-icon {
    transform: rotate(45deg);
    background: #667eea;
    color: white;
  }

  .faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
  }

  .faq-answer.active {
    max-height: 500px;
  }

  .answer-content {
    padding: 0 1.5rem 1.5rem;
    color: #6b7280;
    line-height: 1.6;
  }

  .answer-content p {
    margin: 0;
    font-size: 1rem;
  }

  /* Animation for scroll reveal */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .faq-question {
      padding: 1.25rem;
      font-size: 1rem;
    }

    .question-text {
      margin-right: 0.8rem;
    }

    .faq-icon {
      font-size: 1.3rem;
      width: 20px;
      height: 20px;
    }

    .answer-content {
      padding: 0 1.25rem 1.25rem;
      font-size: 0.95rem;
    }
  }

  @media (max-width: 480px) {
    .faq-question {
      padding: 1rem;
      font-size: 0.95rem;
    }

    .answer-content {
      padding: 0 1rem 1rem;
      font-size: 0.9rem;
    }

    .faq-icon {
      font-size: 1.2rem;
      width: 18px;
      height: 18px;
    }
  }

  /* Focus styles for accessibility */
  .faq-question:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
  }

  /* Smooth animation for content reveal */
  .faq-answer {
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Loading state */
  .faq-item.loading .faq-question {
    opacity: 0.7;
    pointer-events: none;
  }
</style>

<script>
  // FAQ functionality
  document.addEventListener('DOMContentLoaded', function() {
    const faqButtons = document.querySelectorAll('[data-faq-toggle]');
    
    faqButtons.forEach(button => {
      button.addEventListener('click', function() {
        const index = this.getAttribute('data-faq-toggle');
        const content = document.querySelector(`[data-faq-content="${index}"]`);
        const isActive = this.classList.contains('active');
        
        // Close all other FAQ items
        faqButtons.forEach(btn => {
          btn.classList.remove('active');
          const btnIndex = btn.getAttribute('data-faq-toggle');
          const btnContent = document.querySelector(`[data-faq-content="${btnIndex}"]`);
          btnContent?.classList.remove('active');
        });
        
        // Toggle current item
        if (!isActive) {
          this.classList.add('active');
          content?.classList.add('active');
        }
      });
    });

    // Scroll animations for FAQ items
    const animateElements = document.querySelectorAll('.faq-item[data-animate]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          // Add staggered animation delay
          setTimeout(() => {
            entry.target.classList.add('animate');
          }, index * 100);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });

    // Keyboard navigation
    faqButtons.forEach((button, index) => {
      button.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          const nextButton = faqButtons[index + 1];
          nextButton?.focus();
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          const prevButton = faqButtons[index - 1];
          prevButton?.focus();
        }
      });
    });
  });
</script>
