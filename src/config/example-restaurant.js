// Example configuration for a restaurant business
// Copy this content to landingPage.js to see a restaurant-themed landing page

export const landingPageConfig = {
  // Site metadata
  site: {
    title: "Bella Vista Restaurant",
    description: "Authentic Italian cuisine in the heart of downtown. Fresh ingredients, traditional recipes, unforgettable dining experience.",
    keywords: "restaurant, italian food, fine dining, pasta, pizza, downtown restaurant"
  },

  // Hero section
  hero: {
    title: "Authentic Italian Cuisine",
    subtitle: "Where Tradition Meets Flavor",
    description: "Experience the finest Italian dining with fresh ingredients, traditional recipes, and warm hospitality in our cozy downtown location.",
    primaryButton: {
      text: "Make Reservation",
      href: "#contact"
    },
    secondaryButton: {
      text: "View Menu",
      href: "#services"
    }
  },

  // Features section
  features: {
    title: "Why Choose Bella Vista",
    subtitle: "What makes our restaurant special",
    items: [
      {
        title: "Fresh Ingredients",
        description: "We source the finest local and imported ingredients daily to ensure exceptional quality.",
        icon: "🌿"
      },
      {
        title: "Traditional Recipes",
        description: "Authentic family recipes passed down through generations of Italian chefs.",
        icon: "👨‍🍳"
      },
      {
        title: "Cozy Atmosphere",
        description: "Warm, inviting ambiance perfect for romantic dinners and family gatherings.",
        icon: "🕯️"
      },
      {
        title: "Expert Service",
        description: "Our knowledgeable staff provides attentive service and wine pairing recommendations.",
        icon: "🍷"
      }
    ]
  },

  // About section
  about: {
    title: "Our Story",
    description: "Founded in 1985 by the Rossi family, Bella Vista has been serving authentic Italian cuisine for over three decades. Our passion for traditional cooking methods and commitment to quality has made us a beloved destination for food lovers.",
    stats: [
      {
        number: "35+",
        label: "Years of Excellence"
      },
      {
        number: "50,000+",
        label: "Happy Customers"
      },
      {
        number: "200+",
        label: "Signature Dishes"
      },
      {
        number: "4.9★",
        label: "Average Rating"
      }
    ]
  },

  // Services section (Menu categories)
  services: {
    title: "Our Menu",
    subtitle: "Discover our signature dishes and specialties",
    items: [
      {
        title: "Pasta & Risotto",
        description: "Handmade pasta and creamy risottos prepared with traditional techniques.",
        features: ["Spaghetti Carbonara", "Fettuccine Alfredo", "Mushroom Risotto", "Lasagna della Casa"]
      },
      {
        title: "Pizza & Appetizers",
        description: "Wood-fired pizzas and delicious appetizers to start your meal perfectly.",
        features: ["Margherita Pizza", "Antipasto Platter", "Bruschetta", "Calamari Fritti"]
      },
      {
        title: "Main Courses",
        description: "Expertly prepared meat and seafood dishes with authentic Italian flavors.",
        features: ["Osso Buco", "Chicken Parmigiana", "Grilled Branzino", "Veal Marsala"]
      }
    ]
  },

  // Call to action section
  cta: {
    title: "Ready for an Unforgettable Meal?",
    description: "Join us for an authentic Italian dining experience. Reserve your table today and taste the difference tradition makes.",
    button: {
      text: "Make Reservation",
      href: "#contact"
    }
  },

  // Contact section
  contact: {
    title: "Visit Us Today",
    description: "We're open for lunch and dinner. Reservations recommended for dinner service.",
    email: "<EMAIL>",
    phone: "+1 (555) 123-FOOD",
    address: "123 Main Street, Downtown District, City 12345"
  },

  // Footer
  footer: {
    companyName: "Bella Vista Restaurant",
    copyright: "All rights reserved.",
    links: [
      {
        title: "Menu",
        href: "/menu"
      },
      {
        title: "Reservations",
        href: "#contact"
      },
      {
        title: "Private Events",
        href: "/events"
      }
    ],
    social: [
      {
        name: "Instagram",
        href: "https://instagram.com/bellavistarestaurant",
        icon: "📸"
      },
      {
        name: "Facebook",
        href: "https://facebook.com/bellavistarestaurant",
        icon: "📘"
      },
      {
        name: "Yelp",
        href: "https://yelp.com/biz/bella-vista-restaurant",
        icon: "⭐"
      }
    ]
  }
};
