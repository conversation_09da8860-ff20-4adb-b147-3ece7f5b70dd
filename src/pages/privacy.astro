---
import Layout from '../layouts/Layout.astro';
import { appLandingConfig } from '../config/landingPage.js';

// Read the privacy markdown content
const privacyContent = await Astro.glob('../config/privacy.md');
const privacyMarkdown = privacyContent[0];

const { app, seo } = appLandingConfig;
---

<Layout 
  title={`Privacy Policy - ${app.name}`}
  description="Privacy Policy for TaskFlow Pro. Learn how we protect and handle your personal data."
  keywords="privacy policy, data protection, privacy, TaskFlow Pro"
>
  <div class="legal-page">
    <nav class="legal-nav">
      <div class="container">
        <a href="/" class="back-link">
          <span class="back-icon">←</span>
          Back to {app.name}
        </a>
      </div>
    </nav>

    <main class="legal-content">
      <div class="container">
        <div class="content-wrapper">
          <div class="markdown-content" set:html={privacyMarkdown.compiledContent()} />
        </div>
      </div>
    </main>

    <footer class="legal-footer">
      <div class="container">
        <div class="footer-content">
          <p>&copy; {new Date().getFullYear()} {app.name}. All rights reserved.</p>
          <div class="footer-links">
            <a href="/terms">Terms of Service</a>
            <a href="/">Home</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</Layout>

<style>
  .legal-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .legal-nav {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  .back-link:hover {
    color: #5a6fd8;
  }

  .back-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
  }

  .legal-content {
    flex-grow: 1;
    padding: 3rem 0;
    background: #f9fafb;
  }

  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  .markdown-content {
    padding: 3rem;
    line-height: 1.7;
    color: #374151;
  }

  /* Markdown Styles */
  .markdown-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    border-bottom: 3px solid #667eea;
    padding-bottom: 1rem;
  }

  .markdown-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1f2937;
    margin: 2.5rem 0 1rem;
    border-left: 4px solid #667eea;
    padding-left: 1rem;
  }

  .markdown-content h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #374151;
    margin: 2rem 0 0.8rem;
  }

  .markdown-content p {
    margin-bottom: 1.2rem;
    font-size: 1rem;
  }

  .markdown-content ul,
  .markdown-content ol {
    margin: 1rem 0 1.5rem 1.5rem;
  }

  .markdown-content li {
    margin-bottom: 0.5rem;
  }

  .markdown-content strong {
    font-weight: 600;
    color: #1f2937;
  }

  .markdown-content em {
    font-style: italic;
    color: #6b7280;
  }

  .markdown-content code {
    background: #f3f4f6;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
  }

  .markdown-content blockquote {
    border-left: 4px solid #d1d5db;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
  }

  .markdown-content hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 2rem 0;
  }

  .legal-footer {
    background: #1f2937;
    color: white;
    padding: 2rem 0;
  }

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer-links {
    display: flex;
    gap: 1.5rem;
  }

  .footer-links a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .footer-links a:hover {
    color: white;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .markdown-content {
      padding: 2rem;
    }

    .markdown-content h1 {
      font-size: 2rem;
    }

    .markdown-content h2 {
      font-size: 1.5rem;
    }

    .markdown-content h3 {
      font-size: 1.2rem;
    }

    .footer-content {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .footer-links {
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .legal-content {
      padding: 2rem 0;
    }

    .markdown-content {
      padding: 1.5rem;
    }

    .markdown-content h1 {
      font-size: 1.8rem;
    }

    .markdown-content h2 {
      font-size: 1.3rem;
      margin: 2rem 0 0.8rem;
    }

    .content-wrapper {
      border-radius: 8px;
    }
  }

  /* Print styles */
  @media print {
    .legal-nav,
    .legal-footer {
      display: none;
    }

    .legal-content {
      background: white;
    }

    .content-wrapper {
      box-shadow: none;
      border: 1px solid #e5e7eb;
    }

    .markdown-content {
      font-size: 12pt;
      line-height: 1.5;
    }
  }
</style>
