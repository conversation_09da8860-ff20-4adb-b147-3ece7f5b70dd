# Mobile App Landing Page

A comprehensive, modern mobile app landing page built with Astro, featuring sticky navigation, smooth animations, and complete SEO optimization.

## 🚀 Features

### ✨ App-Specific Sections
- **Hero Section** - App name, tagline, icon, and download buttons
- **Screenshots Showcase** - Horizontal scrolling app screenshots with device frames
- **Key Features** - Highlight app functionality with icons and descriptions
- **User Testimonials** - Customer reviews with ratings and avatars
- **Download Section** - App Store and Google Play buttons with QR code
- **FAQ Section** - Expandable questions and answers
- **Legal Pages** - Terms & Conditions and Privacy Policy

### 🧭 Navigation & UX
- **Sticky Navigation** - Always visible app bar with smooth scroll-to-section
- **Mobile Hamburger Menu** - Responsive navigation for mobile devices
- **Smooth Animations** - Fade-in effects as sections come into view
- **Active Link Highlighting** - Navigation links highlight based on scroll position

### 📱 Mobile-First Design
- **Responsive Layout** - Optimized for mobile, tablet, and desktop
- **Touch-Friendly** - Large buttons and touch targets
- **Device Frames** - Screenshots displayed in realistic phone frames
- **Modern UI Patterns** - App-appropriate design language

### 🔍 SEO & Performance
- **Enhanced Meta Tags** - Title, description, keywords, canonical URLs
- **Open Graph Tags** - Optimized for Facebook and social media sharing
- **Twitter Cards** - Rich previews for Twitter
- **Structured Data** - JSON-LD markup for mobile app information
- **Performance Optimized** - Fast loading with optimized images

## 📁 Project Structure

```
src/
├── config/
│   ├── landingPage.js          # Main app configuration
│   ├── terms.md               # Terms of Service content
│   └── privacy.md             # Privacy Policy content
├── components/
│   ├── Navigation.astro       # Sticky navigation with hamburger menu
│   ├── AppLandingPage.astro   # Main landing page component
│   ├── AppScreenshots.astro   # Screenshots showcase section
│   ├── DownloadButtons.astro  # App store download buttons
│   ├── Testimonials.astro     # User reviews and testimonials
│   └── FAQ.astro              # Frequently asked questions
├── layouts/
│   └── Layout.astro           # Enhanced layout with SEO meta tags
└── pages/
    ├── index.astro            # Main landing page
    ├── terms.astro            # Terms of Service page
    └── privacy.astro          # Privacy Policy page
```

## ⚙️ Configuration

All content is managed through `src/config/landingPage.js`:

### App Metadata
```javascript
app: {
  name: "TaskFlow Pro",
  tagline: "Productivity Redefined",
  description: "The ultimate task management app...",
  icon: "📱",
  version: "2.1.0",
  category: "Productivity",
  rating: 4.8,
  downloads: "1M+"
}
```

### SEO Configuration
```javascript
seo: {
  title: "TaskFlow Pro - The Ultimate Productivity App",
  description: "Download TaskFlow Pro and transform...",
  keywords: "task management app, productivity app...",
  ogImage: "/images/app-social-preview.jpg",
  twitterCard: "summary_large_image"
}
```

### Navigation Menu
```javascript
navigation: {
  logo: "TaskFlow Pro",
  links: [
    { text: "Features", href: "#features" },
    { text: "Screenshots", href: "#screenshots" },
    { text: "Reviews", href: "#testimonials" },
    { text: "FAQ", href: "#faq" },
    { text: "Download", href: "#download" }
  ]
}
```

### App Screenshots
```javascript
screenshots: {
  title: "See TaskFlow Pro in Action",
  subtitle: "Beautiful design meets powerful functionality",
  images: [
    {
      src: "/images/screenshot-1.png",
      alt: "TaskFlow Pro main dashboard...",
      caption: "Smart Dashboard"
    }
    // ... more screenshots
  ]
}
```

### Download Buttons
```javascript
download: {
  buttons: {
    ios: {
      text: "Download on the App Store",
      href: "https://apps.apple.com/app/taskflow-pro",
      icon: "🍎",
      badge: "/images/app-store-badge.png"
    },
    android: {
      text: "Get it on Google Play",
      href: "https://play.google.com/store/apps/details?id=com.taskflow.pro",
      icon: "🤖",
      badge: "/images/google-play-badge.png"
    }
  }
}
```

## 🖼️ Required Images

Place these images in the `public/images/` directory:

### App Screenshots (250x500px recommended)
- `screenshot-1.png` - Main dashboard
- `screenshot-2.png` - Task creation
- `screenshot-3.png` - Team collaboration
- `screenshot-4.png` - Analytics
- `screenshot-5.png` - Settings

### Marketing Assets
- `app-hero-mockup.png` - Hero section app mockup (600x600px)
- `app-social-preview.jpg` - Social media preview (1200x630px)
- `download-qr-code.png` - QR code for downloads

### Store Badges
- `app-store-badge.png` - Official App Store badge
- `google-play-badge.png` - Official Google Play badge

### User Avatars (50x50px)
- `avatar-1.jpg` through `avatar-4.jpg` - Testimonial avatars

## 🎨 Customization

### Colors
The app uses a purple gradient theme. To change colors, update the CSS variables in the components:
- Primary: `#667eea`
- Secondary: `#764ba2`
- Background: `#f8fafc`

### Fonts
The landing page uses Inter font family. You can change this in the CSS or add custom fonts.

### Animations
Scroll animations are powered by Intersection Observer API. Customize timing and effects in the component scripts.

## 📱 Mobile Features

### Responsive Navigation
- Desktop: Horizontal menu with hover effects
- Mobile: Hamburger menu with slide-out navigation
- Smooth scroll to sections with offset for fixed header

### Touch Interactions
- Large touch targets for mobile users
- Swipe-friendly screenshot carousel
- Touch-optimized download buttons

### Performance
- Lazy loading for images
- Optimized animations for mobile devices
- Minimal JavaScript for fast loading

## 🔧 Development Commands

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Preview production build
```

## 🚀 Deployment

1. **Build the project**: `npm run build`
2. **Upload the `dist/` folder** to your hosting service
3. **Configure redirects** for `/terms` and `/privacy` routes
4. **Add your actual images** to replace placeholders
5. **Update configuration** with your app's real data

## 📊 SEO Checklist

- ✅ Meta titles and descriptions
- ✅ Open Graph tags for social sharing
- ✅ Twitter Card meta tags
- ✅ Structured data for mobile app
- ✅ Canonical URLs
- ✅ Mobile-friendly design
- ✅ Fast loading times
- ✅ Proper heading hierarchy
- ✅ Alt text for images
- ✅ Semantic HTML structure

## 🎯 Ready to Launch

The mobile app landing page is production-ready with:
- Complete mobile app sections
- Sticky navigation with smooth scrolling
- Responsive design for all devices
- SEO optimization
- Legal pages (Terms & Privacy)
- Configurable content system
- Modern animations and interactions

Simply update the configuration file with your app's information and replace the placeholder images!
