# Quick Logo Setup Guide

## ✅ **Custom Logo System - Ready to Use**

Your mobile app landing page is now configured to use custom logo images instead of Lucide icons.

## 🚀 **Quick Setup (2 Steps)**

### **Step 1: Add Your Logo**
Place your logo file in the `public/images/` directory:
```
public/images/logo.png    (or logo.svg, logo.jpg)
```

**Logo Requirements:**
- Square aspect ratio (1:1)
- 512x512px minimum size
- Under 100KB file size
- PNG with transparency recommended

### **Step 2: Update Configuration (if needed)**
If using a different filename, update `src/config/landingPage.js`:
```javascript
app: {
  icon: {
    type: "image",
    value: "/images/your-logo-name.png"  // Update this path
  }
}
```

## 📍 **Current Configuration**
```javascript
// src/config/landingPage.js
app: {
  icon: {
    type: "image",
    value: "/images/logo.svg"  // Currently using sample logo
  }
}
```

## 🎯 **Logo Appears In:**
- ✅ Navigation bar (48px)
- ✅ Hero section (120px on desktop, scales down on mobile)
- ✅ Maintains glassmorphism styling and purple gradient theme
- ✅ Automatic fallback to default icon if image fails

## 📱 **Responsive Sizes:**
- **Desktop**: 120px (hero), 48px (nav)
- **Tablet**: 96px (hero), 48px (nav)
- **Mobile**: 80px (hero), 48px (nav)
- **Small Mobile**: 64px (hero), 48px (nav)

## 🔄 **Alternative: Use Lucide Icons**
To switch back to icon-based system:
```javascript
app: {
  icon: {
    type: "lucide",
    value: "Smartphone"  // Any Lucide icon name
  }
}
```

## 🧪 **Test Your Logo**
1. Replace the sample logo with your file
2. Run `npm run dev`
3. Check navigation and hero sections
4. Test on mobile devices
5. Verify loading and fallback behavior

## 📖 **Need More Details?**
See `CUSTOM_LOGO_IMPLEMENTATION_GUIDE.md` for comprehensive documentation including:
- Design guidelines
- Advanced configuration options
- Troubleshooting tips
- Performance optimization

## ✨ **Sample Logo Included**
A sample TaskFlow Pro logo is included at `/public/images/logo.svg` to demonstrate the system. Replace it with your actual logo to complete the setup.

**Ready to use!** Your custom logo will appear throughout the landing page with professional styling and responsive behavior.
